# CKEditor Content Parser for Laravel

This package provides a comprehensive solution for parsing and cleaning CKEditor content in your Laravel application. It converts CKEditor's HTML output into clean, properly formatted HTML suitable for display.

## Features

- ✅ **HTML Entity Decoding**: Converts HTML entities like `&mdash;` to proper characters `—`
- ✅ **Markdown Support**: Converts `**bold**` text to `<strong>bold</strong>`
- ✅ **Heading Conversion**: Transforms `### Heading` to proper `<h3>Heading</h3>`
- ✅ **BR Tag Cleanup**: Removes unnecessary `<br />` tags and converts them to proper spacing
- ✅ **Content Sanitization**: Removes dangerous scripts and attributes for security
- ✅ **Plain Text Extraction**: Extracts clean plain text from HTML content
- ✅ **Excerpt Generation**: Creates excerpts with proper length limits
- ✅ **Blade Directives**: Custom Blade directives for easy template usage

## Installation

The parser is already installed in your Laravel application. The files are located at:

- `app/Services/CKEditorParser.php` - Main parser service
- `app/Helpers/CKEditorHelper.php` - Helper functions
- `app/Models/Blog.php` - Updated with parser methods

## Usage

### 1. Using the Service Class

```php
use App\Services\CKEditorParser;

// Parse CKEditor content to clean HTML
$cleanHtml = CKEditorParser::parseToHtml($ckeditorContent);

// Extract plain text
$plainText = CKEditorParser::extractPlainText($ckeditorContent);

// Generate excerpt
$excerpt = CKEditorParser::generateExcerpt($ckeditorContent, 150);

// Clean and sanitize content
$safeHtml = CKEditorParser::clean($ckeditorContent);
```

### 2. Using Helper Functions

```php
// Parse content
$cleanHtml = parse_ckeditor($ckeditorContent);

// Clean content
$safeHtml = clean_ckeditor($ckeditorContent);

// Generate excerpt
$excerpt = ckeditor_excerpt($ckeditorContent, 150);

// Extract plain text
$plainText = ckeditor_plain_text($ckeditorContent);
```

### 3. Using Blade Directives

```blade
{{-- Parse CKEditor content --}}
@ckeditor($blog->content)

{{-- Clean and sanitize content --}}
@ckeditorClean($blog->content)

{{-- Generate excerpt with custom length --}}
@ckeditorExcerpt($blog->content, 200)
```

### 4. Using Model Attributes

The `Blog` model has been enhanced with automatic parsing:

```php
// In your controller or view
$blog = Blog::find(1);

// Get parsed HTML content
echo $blog->parsed_content;

// Get clean HTML content
echo $blog->clean_content;

// Get plain text content
echo $blog->plain_text_content;

// The excerpt attribute now uses the parser
echo $blog->excerpt;
```

### 5. In Blade Templates

```blade
{{-- Display parsed content --}}
<div class="blog-content">
    {!! $blog->parsed_content !!}
</div>

{{-- Display excerpt --}}
<p class="excerpt">{{ $blog->excerpt }}</p>

{{-- Display plain text for meta description --}}
<meta name="description" content="{{ Str::limit($blog->plain_text_content, 160) }}">
```

## Example Transformation

### Input (CKEditor Content)
```html
<h1>The Hidden Journey of Your DNA<br />
Real-World Care</h1>

<p>Imagine unlocking the secrets within your DNA&mdash;insights that could<br />
revolutionize your approach to health, wellness, and disease<br />
prevention. Today, understanding your genetic makeup is no longer reserved for<br />
researchers and academics. Innovations in genetic testing, with a<br />
focus on **pharmacogenomics** (how your genes affect medication<br />
response) and **nutrigenomics** (how your genes interact with<br />
nutrition), are turning individual DNA codes into clear, actionable<br />
health insights.</p>

<p>### How Does Your DNA Leap from the Lab to Your Lifestyle?</p>
```

### Output (Parsed HTML)
```html
<h1>The Hidden Journey of Your DNA
Real-World Care</h1>

<p>Imagine unlocking the secrets within your DNA—insights that could revolutionize your approach to health, wellness, and disease prevention. Today, understanding your genetic makeup is no longer reserved for researchers and academics. Innovations in genetic testing, with a focus on <strong>pharmacogenomics</strong> (how your genes affect medication response) and <strong>nutrigenomics</strong> (how your genes interact with nutrition), are turning individual DNA codes into clear, actionable health insights.</p>

<h3>How Does Your DNA Leap from the Lab to Your Lifestyle?</h3>
```

## Security Features

The parser includes built-in security features:

- Removes `<script>` tags
- Strips dangerous event handlers (`onclick`, `onload`, etc.)
- Filters out `javascript:` and `vbscript:` URLs
- Only allows safe HTML tags and attributes

## Testing

Run the tests to ensure everything works correctly:

```bash
php artisan test --filter=CKEditorParserTest
```

## Demo

Run the demo script to see the parser in action:

```bash
php demo_ckeditor_parser.php
```

This will show you a before/after comparison using your sample content.

## Configuration

The parser uses sensible defaults, but you can customize the allowed HTML tags in the `clean()` method by modifying the `$allowedTags` variable in `app/Services/CKEditorParser.php`.

## Performance

The parser is optimized for performance and handles large content efficiently. It uses regex patterns and callbacks to process content in a single pass where possible.
