<?php

use App\Http\Controllers\BlogController;
use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

Route::get('/', [BlogController::class, 'index'])->name('home');

Route::get('/dashboard', function () {
    return redirect()->route('blogs.dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Blog routes - /blogs route removed, / is now the main blog index

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Authenticated blog routes
    Route::get('/my-blogs', [BlogController::class, 'dashboard'])->name('blogs.dashboard');
    Route::post('/blogs', [BlogController::class, 'store'])->name('blogs.store');
    Route::put('/blogs/{blog}', [BlogController::class, 'update'])->name('blogs.update');
    Route::delete('/blogs/{blog}', [BlogController::class, 'destroy'])->name('blogs.destroy');
});

// Public blog routes (no auth required for testing)
Route::get('/blogs/create', [BlogController::class, 'create'])->name('blogs.create')->middleware('auth');
Route::get('/blogs/{blog}/edit', [BlogController::class, 'edit'])->name('blogs.edit')->middleware('auth');

// Blog show route (must be after specific routes like /blogs/create)
Route::get('/blogs/{blog}', [BlogController::class, 'show'])->name('blogs.show');

require __DIR__.'/auth.php';
