@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for ScylexLab-inspired design */

/* Light theme base styles */
body {
    background-color: #ffffff !important;
    color: #212529 !important;
    font-family: 'Figtree', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f8f9fa;
}

::-webkit-scrollbar-thumb {
    background: #4ade80;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #22c55e;
}

/* Navigation styles */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: #212529 !important;
}

.nav-link {
    color: #212529 !important;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #4ade80 !important;
}

/* Card styles */
.card {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 12px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-img-top {
    height: 200px;
    object-fit: cover;
    border-radius: 12px 12px 0 0;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    color: #212529;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.card-text {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 1rem;
}

/* Button styles */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    padding: 0.5rem 1rem;
}

.btn-primary {
    background-color: #4ade80;
    color: #000000;
}

.btn-primary:hover {
    background-color: #22c55e;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(74, 222, 128, 0.3);
}

.btn-outline-primary {
    border: 1px solid #4ade80;
    color: #4ade80;
    background-color: transparent;
}

.btn-outline-primary:hover {
    background-color: #4ade80;
    color: #000000;
}

/* Blog post specific styles */
.blog-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: #868e96;
}

.blog-meta .author {
    color: #4ade80;
    font-weight: 500;
}

.blog-meta .date {
    color: #6c757d;
}

/* Hero section */
.hero-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    padding: 4rem 0;
    margin-bottom: 3rem;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    color: #212529;
    margin-bottom: 1rem;
    text-align: center;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: #6c757d;
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

/* Form styles */
.form-control {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    color: #212529;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    background-color: #ffffff;
    border-color: #4ade80;
    color: #212529;
    box-shadow: 0 0 0 0.2rem rgba(74, 222, 128, 0.25);
}

.form-control::placeholder {
    color: #868e96;
}

/* Alert styles */
.alert {
    border-radius: 8px;
    border: none;
    font-weight: 500;
}

.alert-success {
    background-color: rgba(74, 222, 128, 0.1);
    color: #4ade80;
    border-left: 4px solid #4ade80;
}

.alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border-left: 4px solid #ef4444;
}

/* Footer styles */
.footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 2rem 0;
    margin-top: 4rem;
}

.footer-text {
    color: #6c757d;
    text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .card-title {
        font-size: 1.1rem;
    }

    .blog-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

/* Loading animation */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #333333;
    border-top: 4px solid #4ade80;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom utilities */
.text-gradient {
    background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(26, 26, 26, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(74, 222, 128, 0.1);
}

.shadow-glow {
    box-shadow: 0 0 20px rgba(74, 222, 128, 0.1);
}
