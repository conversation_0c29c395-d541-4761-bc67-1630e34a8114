<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    {{-- Basic SEO Meta Tags --}}
    <title>{{ $metaTitle ?? ($blog->meta_title ?? $blog->title ?? config('app.name', 'Laravel')) }}</title>
    <meta name="description" content="{{ $metaDescription ?? ($blog->meta_description ?? $blog->excerpt ?? 'A blog website built with Laravel') }}">
    <meta name="keywords" content="{{ $metaKeywords ?? ($blog->meta_keywords ?? '') }}">
    
    {{-- Canonical URL --}}
    @if(isset($blog) && $blog->canonical_url)
        <link rel="canonical" href="{{ $blog->canonical_url }}">
    @else
        <link rel="canonical" href="{{ url()->current() }}">
    @endif

    {{-- Open Graph Meta Tags --}}
    <meta property="og:type" content="article">
    <meta property="og:title" content="{{ $ogTitle ?? ($blog->og_title ?? $blog->title ?? config('app.name')) }}">
    <meta property="og:description" content="{{ $ogDescription ?? ($blog->og_description ?? $blog->excerpt ?? 'A blog website built with Laravel') }}">
    <meta property="og:url" content="{{ url()->current() }}">
    @if(isset($blog) && $blog->og_image)
        <meta property="og:image" content="{{ asset('storage/' . $blog->og_image) }}">
    @elseif(isset($blog) && $blog->featured_image)
        <meta property="og:image" content="{{ asset('storage/' . $blog->featured_image) }}">
    @endif
    <meta property="og:site_name" content="{{ config('app.name') }}">
    @if(isset($blog))
        <meta property="article:author" content="{{ $blog->user->name }}">
        <meta property="article:published_time" content="{{ $blog->published_at?->toISOString() }}">
        <meta property="article:modified_time" content="{{ $blog->updated_at->toISOString() }}">
    @endif

    {{-- Twitter Card Meta Tags --}}
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ $twitterTitle ?? ($blog->twitter_title ?? $blog->title ?? config('app.name')) }}">
    <meta name="twitter:description" content="{{ $twitterDescription ?? ($blog->twitter_description ?? $blog->excerpt ?? 'A blog website built with Laravel') }}">
    @if(isset($blog) && $blog->twitter_image)
        <meta name="twitter:image" content="{{ asset('storage/' . $blog->twitter_image) }}">
    @elseif(isset($blog) && $blog->featured_image)
        <meta name="twitter:image" content="{{ asset('storage/' . $blog->featured_image) }}">
    @endif

    {{-- Additional Meta Tags --}}
    <meta name="robots" content="index, follow">
    <meta name="author" content="{{ isset($blog) ? $blog->user->name : config('app.name') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    {{-- Additional head content --}}
    @stack('head')
</head>
<body class="font-sans antialiased">
    <div class="min-h-screen">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container">
                <div class="d-flex w-100">
                    <!-- Logo -->
                    <div class="me-auto">
                        <a href="{{ route('home') }}" class="navbar-brand d-flex align-items-center text-decoration-none">
                            <img src="https://scylexlab.com/build/images/logo.webp" alt="SCYLEX" class="me-2" style="height: 32px; width: auto;">
                            <span class="text-gradient fw-bold">SCYLEX</span>
                        </a>
                    </div>

                    <!-- Navigation Links -->
                    <div class="navbar-nav d-none d-lg-flex">
                        <a href="{{ route('home') }}" class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}">
                            Home
                        </a>
                    </div>

                    <!-- Settings Dropdown -->
                    <div class="d-none d-lg-block">
                        @auth
                            <div class="dropdown">
                                <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    {{ Auth::user()->name }}
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="{{ route('blogs.dashboard') }}">My Blogs</a></li>
                                    <li><a class="dropdown-item" href="{{ route('blogs.create') }}">Create Blog</a></li>
                                    <li><a class="dropdown-item" href="{{ route('profile.edit') }}">Profile</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form method="POST" action="{{ route('logout') }}">
                                            @csrf
                                            <button type="submit" class="dropdown-item">Log Out</button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                        @else
                            <div class="d-flex gap-2">
                                <a href="{{ route('login') }}" class="btn btn-outline-primary btn-sm">Login</a>
                                <a href="{{ route('register') }}" class="btn btn-primary btn-sm">Register</a>
                            </div>
                        @endauth
                    </div>

                    <!-- Mobile menu button -->
                    <button class="btn btn-outline-primary d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                </div>

                <!-- Mobile Navigation -->
                <div class="collapse navbar-collapse d-lg-none" id="navbarNav">
                    <div class="navbar-nav me-auto">
                        <a href="{{ route('home') }}" class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}">
                            Home
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <main class="container py-4">
            @if (session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if (session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            {{ $slot }}
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="container">
                <p class="footer-text mb-0">
                    © {{ date('Y') }} {{ config('app.name', 'Laravel') }}. All rights reserved.
                </p>
            </div>
        </footer>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    {{-- Additional scripts --}}
    @stack('scripts')
</body>
</html>
