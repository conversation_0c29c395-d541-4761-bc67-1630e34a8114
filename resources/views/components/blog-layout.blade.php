<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    {{-- Basic SEO Meta Tags --}}
    <title>{{ $metaTitle ?? ($blog->meta_title ?? $blog->title ?? config('app.name', 'Laravel')) }}</title>
    <meta name="description" content="{{ $metaDescription ?? ($blog->meta_description ?? $blog->excerpt ?? 'A blog website built with Laravel') }}">
    <meta name="keywords" content="{{ $metaKeywords ?? ($blog->meta_keywords ?? '') }}">
    
    {{-- Canonical URL --}}
    @if(isset($blog) && $blog->canonical_url)
        <link rel="canonical" href="{{ $blog->canonical_url }}">
    @else
        <link rel="canonical" href="{{ url()->current() }}">
    @endif

    {{-- Open Graph Meta Tags --}}
    <meta property="og:type" content="article">
    <meta property="og:title" content="{{ $ogTitle ?? ($blog->og_title ?? $blog->title ?? config('app.name')) }}">
    <meta property="og:description" content="{{ $ogDescription ?? ($blog->og_description ?? $blog->excerpt ?? 'A blog website built with Laravel') }}">
    <meta property="og:url" content="{{ url()->current() }}">
    @if(isset($blog) && $blog->og_image)
        <meta property="og:image" content="{{ asset('storage/' . $blog->og_image) }}">
    @elseif(isset($blog) && $blog->featured_image)
        <meta property="og:image" content="{{ asset('storage/' . $blog->featured_image) }}">
    @endif
    <meta property="og:site_name" content="{{ config('app.name') }}">
    @if(isset($blog))
        <meta property="article:author" content="{{ $blog->user->name }}">
        <meta property="article:published_time" content="{{ $blog->published_at?->toISOString() }}">
        <meta property="article:modified_time" content="{{ $blog->updated_at->toISOString() }}">
    @endif

    {{-- Twitter Card Meta Tags --}}
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ $twitterTitle ?? ($blog->twitter_title ?? $blog->title ?? config('app.name')) }}">
    <meta name="twitter:description" content="{{ $twitterDescription ?? ($blog->twitter_description ?? $blog->excerpt ?? 'A blog website built with Laravel') }}">
    @if(isset($blog) && $blog->twitter_image)
        <meta name="twitter:image" content="{{ asset('storage/' . $blog->twitter_image) }}">
    @elseif(isset($blog) && $blog->featured_image)
        <meta name="twitter:image" content="{{ asset('storage/' . $blog->featured_image) }}">
    @endif

    {{-- Additional Meta Tags --}}
    <meta name="robots" content="index, follow">
    <meta name="author" content="{{ isset($blog) ? $blog->user->name : config('app.name') }}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- CK Editor CDN -->
    <script src="https://cdn.ckeditor.com/4.22.1/standard/ckeditor.js"></script>

    {{-- Additional head content --}}
    @stack('head')
    
    <style>
        body {
            background-color: #ffffff !important;
            color: #212529 !important;
        }
        .hero-bg {
            background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('{{ asset('images/hero-bg.webp') }}');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            min-height: 400px;
            position: relative;
        }
        .navbar {
            background: transparent !important;
            border: none !important;
            box-shadow: none !important;
        }
        .navbar .nav-link {
            color: white !important;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }
        .navbar .nav-link:hover {
            color: #4ade80 !important;
        }
        .navbar .dropdown-menu {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .navbar .dropdown-item {
            color: #212529;
        }
        .navbar .dropdown-item:hover {
            background-color: #f8f9fa;
            color: #212529;
        }
        .scylex-green { color: #4ade80; }
        .scylex-green-bg { background-color: #4ade80; }
        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .card {
            background: #ffffff;
            border: 1px solid #dee2e6;
            color: #212529;
        }
        .text-dark {
            color: #212529 !important;
        }
        .text-muted {
            color: #6c757d !important;
        }
        .bg-light {
            background-color: #f8f9fa !important;
        }
        .bg-info {
            background-color: #4ade80 !important;
            color: #000000 !important;
        }
        .btn-outline-info {
            border-color: #4ade80;
            color: #4ade80;
        }
        .btn-outline-info:hover {
            background-color: #4ade80;
            color: #000000;
        }
        .badge {
            background-color: #4ade80 !important;
            color: #000000 !important;
        }
        #content {
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            padding: 0.5rem;
            min-height: 200px;
            background-color: #ffffff;
            font-family: inherit;
        }
        #content:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
    </style>
</head>
<body class="bg-white">
    <!-- Integrated Navigation (minimal) -->
    <nav class="navbar navbar-expand-lg navbar-light bg-transparent" style="position: absolute; top: 0; left: 0; right: 0; z-index: 1000; background: transparent !important;">
        <div class="container">
            <!-- Logo -->
            <a class="navbar-brand d-flex align-items-center" href="{{ route('home') }}">
                <img src="https://scylexlab.com/build/images/logo.webp" alt="SCYLEX" class="me-2" style="height: 32px; width: auto;">
                <span class="fw-semibold d-none d-sm-inline text-white">SCYLEX</span>
            </a>

            <!-- Mobile toggle button -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation Links -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('home') }}" style="color: white !important;">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" style="color: white !important;">About Us</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" style="color: white !important;">Research & Innovation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" style="color: white !important;">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" style="color: white !important;">Consent Form</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" style="color: white !important;">Book a Call</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" style="color: white !important;">Products</a>
                    </li>
                </ul>

                <!-- Right side -->
                <ul class="navbar-nav">
                    @auth
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" style="color: white !important;">
                                {{ Auth::user()->name }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('blogs.dashboard') }}">My Blogs</a></li>
                                <li><a class="dropdown-item" href="{{ route('profile.edit') }}">Profile</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit" class="dropdown-item">Log Out</button>
                                    </form>
                                </li>
                            </ul>
                        </li>
                    @else
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('login') }}" style="color: white !important;">Login</a>
                        </li>
                    @endauth
                </ul>
            </div>
        </div>
    </nav>

        <!-- Page Content -->
        <main class="relative">
            @if (session('success'))
                <div class="container py-4">
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            @endif

            @if (session('error'))
                <div class="container py-4">
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            @endif

            {{ $slot }}
        </main>
    </div>

    {{-- Additional scripts --}}
    @stack('scripts')
</body>
</html>
