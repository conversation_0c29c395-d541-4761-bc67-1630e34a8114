<x-blog-layout>
    <div class="py-12">
        <div class="mx-auto max-w-4xl sm:px-6 lg:px-8">
            <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="flex justify-between items-center mb-6">
                        <h1 class="text-3xl font-bold text-gray-900">Create New Blog Post</h1>
                        <a href="{{ route('blogs.dashboard') }}" class="text-gray-600 hover:text-gray-800">
                            ← Back to My Blogs
                        </a>
                    </div>

                    <form method="POST" action="{{ route('blogs.store') }}" enctype="multipart/form-data" class="space-y-6">
                        @csrf

                        <!-- Basic Blog Information -->
                        <div class="p-6 bg-gray-50 rounded-lg">
                            <h2 class="mb-4 text-xl font-semibold text-gray-900">Basic Information</h2>
                            
                            <!-- Title -->
                            <div class="mb-4">
                                <label for="title" class="block mb-2 text-sm font-medium text-gray-700">Title *</label>
                                <input type="text" 
                                       name="title" 
                                       id="title" 
                                       value="{{ old('title') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('title') border-red-500 @enderror"
                                       required>
                                @error('title')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Slug -->
                            <div class="mb-4">
                                <label for="slug" class="block mb-2 text-sm font-medium text-gray-700">Slug (URL)</label>
                                <input type="text" 
                                       name="slug" 
                                       id="slug" 
                                       value="{{ old('slug') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('slug') border-red-500 @enderror"
                                       placeholder="Leave empty to auto-generate from title">
                                @error('slug')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">URL-friendly version of the title. Leave empty to auto-generate.</p>
                            </div>

                            <!-- Excerpt -->
                            <div class="mb-4">
                                <label for="excerpt" class="block mb-2 text-sm font-medium text-gray-700">Excerpt</label>
                                <textarea name="excerpt" 
                                          id="excerpt" 
                                          rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('excerpt') border-red-500 @enderror"
                                          placeholder="Brief summary of your blog post">{{ old('excerpt') }}</textarea>
                                @error('excerpt')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">Short description that appears in blog listings. Leave empty to auto-generate from content.</p>
                            </div>

                            <!-- Content -->
                            <div class="mb-4">
                                <label for="content" class="block mb-2 text-sm font-medium text-gray-700">Content *</label>
                                <textarea name="content" id="content-textarea" style="display: none;">{{ old('content') }}</textarea>
                                <div id="content-editor" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('content') border-red-500 @enderror" style="min-height: 300px;">
                                    {{ old('content') }}
                                </div>
                                @error('content')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Featured Image -->
                            <div class="mb-4">
                                <label for="featured_image" class="block mb-2 text-sm font-medium text-gray-700">Featured Image</label>
                                <input type="file" 
                                       name="featured_image" 
                                       id="featured_image" 
                                       accept="image/*"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('featured_image') border-red-500 @enderror">
                                @error('featured_image')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">Upload an image to display with your blog post (JPEG, PNG, JPG, GIF, max 2MB).</p>
                            </div>

                            <!-- Publishing Options -->
                            <div class="mb-4">
                                <div class="flex items-center">
                                    <input type="checkbox" 
                                           name="is_published" 
                                           id="is_published" 
                                           value="1"
                                           {{ old('is_published') ? 'checked' : '' }}
                                           class="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                    <label for="is_published" class="block ml-2 text-sm text-gray-900">
                                        Publish immediately
                                    </label>
                                </div>
                                <p class="mt-1 text-sm text-gray-500">Uncheck to save as draft.</p>
                            </div>
                        </div>

                        <!-- SEO Settings -->
                        <div class="p-6 bg-blue-50 rounded-lg">
                            <h2 class="mb-4 text-xl font-semibold text-gray-900">SEO Settings</h2>
                            
                            <!-- Meta Title -->
                            <div class="mb-4">
                                <label for="meta_title" class="block mb-2 text-sm font-medium text-gray-700">Meta Title</label>
                                <input type="text" 
                                       name="meta_title" 
                                       id="meta_title" 
                                       value="{{ old('meta_title') }}"
                                       maxlength="60"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('meta_title') border-red-500 @enderror">
                                @error('meta_title')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">Title that appears in search results (recommended: 50-60 characters).</p>
                            </div>

                            <!-- Meta Description -->
                            <div class="mb-4">
                                <label for="meta_description" class="block mb-2 text-sm font-medium text-gray-700">Meta Description</label>
                                <textarea name="meta_description" 
                                          id="meta_description" 
                                          rows="3"
                                          maxlength="160"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('meta_description') border-red-500 @enderror">{{ old('meta_description') }}</textarea>
                                @error('meta_description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">Description that appears in search results (recommended: 150-160 characters).</p>
                            </div>

                            <!-- Meta Keywords -->
                            <div class="mb-4">
                                <label for="meta_keywords" class="block mb-2 text-sm font-medium text-gray-700">Meta Keywords</label>
                                <input type="text" 
                                       name="meta_keywords" 
                                       id="meta_keywords" 
                                       value="{{ old('meta_keywords') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('meta_keywords') border-red-500 @enderror"
                                       placeholder="keyword1, keyword2, keyword3">
                                @error('meta_keywords')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">Comma-separated keywords related to your content.</p>
                            </div>

                            <!-- Canonical URL -->
                            <div class="mb-4">
                                <label for="canonical_url" class="block mb-2 text-sm font-medium text-gray-700">Canonical URL</label>
                                <input type="url" 
                                       name="canonical_url" 
                                       id="canonical_url" 
                                       value="{{ old('canonical_url') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('canonical_url') border-red-500 @enderror">
                                @error('canonical_url')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">Preferred URL for this content (leave empty to use default).</p>
                            </div>
                        </div>

                        <!-- Open Graph Settings -->
                        <div class="p-6 bg-green-50 rounded-lg">
                            <h2 class="mb-4 text-xl font-semibold text-gray-900">Open Graph (Facebook) Settings</h2>
                            
                            <!-- OG Title -->
                            <div class="mb-4">
                                <label for="og_title" class="block mb-2 text-sm font-medium text-gray-700">Open Graph Title</label>
                                <input type="text" 
                                       name="og_title" 
                                       id="og_title" 
                                       value="{{ old('og_title') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('og_title') border-red-500 @enderror">
                                @error('og_title')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">Title when shared on Facebook (leave empty to use main title).</p>
                            </div>

                            <!-- OG Description -->
                            <div class="mb-4">
                                <label for="og_description" class="block mb-2 text-sm font-medium text-gray-700">Open Graph Description</label>
                                <textarea name="og_description" 
                                          id="og_description" 
                                          rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('og_description') border-red-500 @enderror">{{ old('og_description') }}</textarea>
                                @error('og_description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">Description when shared on Facebook.</p>
                            </div>

                            <!-- OG Image -->
                            <div class="mb-4">
                                <label for="og_image" class="block mb-2 text-sm font-medium text-gray-700">Open Graph Image</label>
                                <input type="file" 
                                       name="og_image" 
                                       id="og_image" 
                                       accept="image/*"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('og_image') border-red-500 @enderror">
                                @error('og_image')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">Image for Facebook sharing (recommended: 1200x630px).</p>
                            </div>
                        </div>

                        <!-- Twitter Settings -->
                        <div class="p-6 bg-purple-50 rounded-lg">
                            <h2 class="mb-4 text-xl font-semibold text-gray-900">Twitter Card Settings</h2>
                            
                            <!-- Twitter Title -->
                            <div class="mb-4">
                                <label for="twitter_title" class="block mb-2 text-sm font-medium text-gray-700">Twitter Title</label>
                                <input type="text" 
                                       name="twitter_title" 
                                       id="twitter_title" 
                                       value="{{ old('twitter_title') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('twitter_title') border-red-500 @enderror">
                                @error('twitter_title')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">Title when shared on Twitter (leave empty to use main title).</p>
                            </div>

                            <!-- Twitter Description -->
                            <div class="mb-4">
                                <label for="twitter_description" class="block mb-2 text-sm font-medium text-gray-700">Twitter Description</label>
                                <textarea name="twitter_description" 
                                          id="twitter_description" 
                                          rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('twitter_description') border-red-500 @enderror">{{ old('twitter_description') }}</textarea>
                                @error('twitter_description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">Description when shared on Twitter.</p>
                            </div>

                            <!-- Twitter Image -->
                            <div class="mb-4">
                                <label for="twitter_image" class="block mb-2 text-sm font-medium text-gray-700">Twitter Image</label>
                                <input type="file" 
                                       name="twitter_image" 
                                       id="twitter_image" 
                                       accept="image/*"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('twitter_image') border-red-500 @enderror">
                                @error('twitter_image')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">Image for Twitter sharing (recommended: 1200x600px).</p>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end items-center pt-6 space-x-4">
                            <a href="{{ route('blogs.dashboard') }}" 
                               class="px-4 py-2 text-sm font-medium text-gray-700 bg-white rounded-md border border-gray-300 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Cancel
                            </a>
                            <button type="submit" 
                                    class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md border border-transparent shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Create Blog Post
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        // Auto-generate slug from title
        document.getElementById('title').addEventListener('input', function() {
            const title = this.value;
            const slug = title.toLowerCase()
                .replace(/[^a-z0-9 -]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            document.getElementById('slug').value = slug;
        });

        // Initialize CKEditor with better error handling
        function initializeCKEditor() {
            if (typeof CKEDITOR !== 'undefined') {
                try {
                    const editor = CKEDITOR.replace('content-textarea', {
                        height: 300,
                        toolbar: [
                            { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText', 'PasteFromWord', '-', 'Undo', 'Redo'] },
                            { name: 'editing', items: ['Find', 'Replace', '-', 'SelectAll'] },
                            { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript', '-', 'RemoveFormat'] },
                            { name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', '-', 'Blockquote', '-', 'JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },
                            { name: 'links', items: ['Link', 'Unlink', 'Anchor'] },
                            { name: 'insert', items: ['Image', 'Table', 'HorizontalRule', 'SpecialChar'] },
                            { name: 'styles', items: ['Styles', 'Format', 'Font', 'FontSize'] },
                            { name: 'colors', items: ['TextColor', 'BGColor'] },
                            { name: 'tools', items: ['Maximize', 'ShowBlocks'] }
                        ],
                        removeButtons: 'Save,NewPage,Preview,Print,Templates',
                        extraAllowedContent: 'div(*); p(*); span(*); img(*); table(*); tr(*); td(*); th(*); tbody(*); thead(*); tfoot(*);',
                        allowedContent: true
                    });

                    // Hide the original textarea and show the editor container
                    const editorContainer = document.getElementById('content-editor');
                    if (editorContainer) {
                        editorContainer.style.display = 'none';
                    }

                    // Sync CKEditor content to hidden textarea on form submit
                    document.querySelector('form').addEventListener('submit', function(e) {
                        const contentTextarea = document.getElementById('content-textarea');
                        if (editor && editor.getData) {
                            contentTextarea.value = editor.getData();
                        }

                        // Validate content before submission
                        if (!contentTextarea.value.trim()) {
                            e.preventDefault();
                            alert('Content is required. Please add some content to your blog post.');
                            if (editorContainer) {
                                editorContainer.style.border = '2px solid #ef4444';
                                editorContainer.style.display = 'block';
                            }
                            return false;
                        }
                    });

                    console.log('CKEditor initialized successfully');
                } catch (error) {
                    console.error('Error initializing CKEditor:', error);
                    // Fallback to regular textarea
                    const editorContainer = document.getElementById('content-editor');
                    const contentTextarea = document.getElementById('content-textarea');
                    if (editorContainer && contentTextarea) {
                        editorContainer.style.display = 'block';
                        contentTextarea.style.display = 'block';
                    }
                }
            } else {
                console.error('CKEditor not loaded. Please check your internet connection and try again.');
                // Show fallback textarea
                const editorContainer = document.getElementById('content-editor');
                const contentTextarea = document.getElementById('content-textarea');
                if (editorContainer && contentTextarea) {
                    editorContainer.style.display = 'block';
                    contentTextarea.style.display = 'block';
                }
            }
        }

        // Initialize CKEditor when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Wait a bit for CKEditor to load
            setTimeout(initializeCKEditor, 100);
        });

        // Also try to initialize when CKEditor script loads
        if (typeof CKEDITOR !== 'undefined') {
            CKEDITOR.on('loaded', initializeCKEditor);
        }
    </script>
    @endpush
</x-blog-layout>
