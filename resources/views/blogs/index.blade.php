<x-blog-layout>
    <!-- Integrated Hero Header Section -->
    <div class="hero-bg position-relative d-flex align-items-center justify-content-center" style="height: 400px; margin-top: -1px;">
        <div class="text-center text-white">
            <h1 class="mb-4 display-3 fw-bold">Our Blogs</h1>
            <nav class="fs-5">
                <a href="https://scylexlab.com" class="text-white text-decoration-none">Home</a>
                <span class="mx-2">-</span>
                <span class="text-success">Blogs</span>
            </nav>
        </div>
    </div>

    <!-- Blog Content -->
    <div class="py-5 bg-light">
        <div class="container">

            @if($blogs->count() > 0)
                <div class="row g-4">
                    @foreach($blogs as $blog)
                        <div class="col-12">
                            <div class="border-0 shadow-sm card h-100 blog-card">
                                <div class="row g-0">
                                    @if($blog->featured_image)
                                        <div class="col-md-4">
                                            <div class="overflow-hidden position-relative h-100" style="min-height: 300px;">
                                                <img src="{{ asset('storage/' . $blog->featured_image) }}" 
                                                     alt="{{ $blog->title }}" 
                                                     class="img-fluid w-100 h-100 object-fit-cover blog-image">
                                            </div>
                                        </div>
                                    @endif
                                    
                                    <div class="col-md-8">
                                        <div class="p-4 card-body d-flex flex-column h-100">
                                            <div class="mb-3 d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 24px; height: 24px;">
                                                        <i class="text-white bi bi-person-fill" style="font-size: 12px;"></i>
                                                    </div>
                                                    <span class="fw-medium text-muted small">Scylex Lab</span>
                                                </div>
                                                <span class="badge bg-info text-dark">
                                                  @if($blog->published_at)
                                                    {{ $blog->published_at->format('d-M-Y') }}
                                                  @else
                                                    Unpublished
                                                  @endif
                                                </span>
                                            </div>
                                            
                                            <h2 class="mb-3 card-title h4 fw-bold">
                                                <a href="{{ route('blogs.show', $blog) }}" class="text-decoration-none text-dark">
                                                    {{ $blog->title }}
                                                </a>
                                            </h2>
                                            
                                            <p class="mb-4 card-text text-muted flex-grow-1">
                                                {{ $blog->excerpt }}
                                            </p>
                                            
                                            <div class="mt-auto">
                                                <a href="{{ route('blogs.show', $blog) }}" 
                                                   class="btn btn-outline-info btn-sm">
                                                    Read More <i class="bi bi-arrow-right ms-1"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-5 d-flex justify-content-center">
                    {{ $blogs->links() }}
                </div>
            @else
                <div class="py-5 text-center">
                    <div class="mx-auto" style="max-width: 400px;">
                        <i class="mb-4 bi bi-file-text display-1 text-muted"></i>
                        <h3 class="mb-3 h4 fw-medium">No blogs yet</h3>
                        <p class="mb-4 text-muted">Get started by creating your first blog post.</p>
                        @auth
                            <p class="text-muted">Use the "Create Blog" button in the top navigation to get started.</p>
                        @endauth
                    </div>
                </div>
            @endif
        </div>
    </div>

    @push('head')
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .hero-bg {
            background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('{{ asset('images/hero-bg.webp') }}');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }
        .blog-card:hover {
            transform: translateY(-5px);
            transition: all 0.3s ease;
        }
        .blog-image {
            transition: transform 0.5s ease;
        }
        .blog-card:hover .blog-image {
            transform: scale(1.1);
        }
        .object-fit-cover {
            object-fit: cover;
        }
    </style>
    @endpush
</x-blog-layout>
