<x-blog-layout :blog="$blog">
    <!-- Blog Content Container -->
    <div class="py-5 bg-light min-vh-100">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="overflow-hidden border-0 shadow-sm card">
                        @if($blog->featured_image)
                            <div class="overflow-hidden position-relative" style="height: 400px;">
                                <img src="{{ asset('storage/' . $blog->featured_image) }}" 
                                     alt="{{ $blog->title }}" 
                                     class="img-fluid w-100 h-100 object-fit-cover">
                            </div>
                        @endif
                        
                        <div class="p-4 card-body p-md-5">
                            <!-- Author and Date -->
                            <div class="mb-4 d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 24px; height: 24px;">
                                        <i class="text-white bi bi-person-fill" style="font-size: 12px;"></i>
                                    </div>
                                    <span class="fw-medium text-muted small">Scylex Lab</span>
                                </div>
                                <span class="badge bg-info text-dark">
                                    @if($blog->published_at)
                                      {{ $blog->published_at->format('d-M-Y') }}
                                    @else
                                      Unpublished
                                    @endif
                                </span>
                            </div>

                            <!-- Title -->
                            <h1 class="mb-4 display-5 fw-bold text-dark lh-sm">
                                {{ $blog->title }}
                            </h1>

                            <!-- Content -->
                            <div class="mb-5 fs-6 text-muted lh-lg">
                                {!! $blog->parsed_content !!}
                            </div>

                            @auth
                                @if($blog->user_id === Auth::id())
                                    <!-- Edit/Delete Actions for Author -->
                                    <div class="pt-4 mt-5 border-top">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="text-muted small">You are the author of this post</span>
                                            <div class="gap-2 d-flex">
                                                <a href="{{ route('blogs.edit', $blog) }}" 
                                                   class="btn btn-primary btn-sm">
                                                    <i class="bi bi-pencil me-1"></i>
                                                    Edit Post
                                                </a>
                                                <form method="POST" action="{{ route('blogs.destroy', $blog) }}" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" 
                                                            onclick="return confirm('Are you sure you want to delete this blog?')"
                                                            class="btn btn-danger btn-sm">
                                                        <i class="bi bi-trash me-1"></i>
                                                        Delete Post
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            @endauth

                            <!-- Back to Blogs -->
                            <div class="pt-4 mt-5 border-top">
                                <a href="{{ route('home') }}"
                                   class="btn btn-outline-info">
                                    <i class="bi bi-arrow-left me-2"></i>
                                    Back to all blogs
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('head')
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .object-fit-cover {
            object-fit: cover;
        }
    </style>
    @endpush
</x-blog-layout>
