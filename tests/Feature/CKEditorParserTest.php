<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\CKEditorParser;

class CKEditorParserTest extends TestCase
{
    public function test_parse_ckeditor_content_to_html()
    {
        $sampleContent = '<h1>The Hidden Journey of Your DNA: From Lab Research to Transforming Your<br />
Real-World Care</h1>

<p>Imagine unlocking the secrets within your DNA&mdash;insights that could<br />
revolutionize your approach to health, wellness, and disease<br />
prevention. What if a simple test could tell you not just about your<br />
ancestry, but about the unique ways your body processes medication or<br />
responds to specific nutrients? For millions across urban America,<br />
this isn&#39;t science fiction&mdash;it&#39;s the new frontier in personalized<br />
healthcare.</p>

<p>Today, understanding your genetic makeup is no longer reserved for<br />
researchers and academics. Innovations in genetic testing, with a<br />
focus on **pharmacogenomics** (how your genes affect medication<br />
response) and **nutrigenomics** (how your genes interact with<br />
nutrition), are turning individual DNA codes into clear, actionable<br />
health insights. Companies like Scylex Labs are making this<br />
transformation accessible, HIPAA-compliant, and personalized&mdash;helping<br />
health-conscious adults pave the road to stronger, safer, and more<br />
informed healthcare choices.</p>

<p>### How Does Your DNA Leap from the Lab to Your Lifestyle?</p>';

        $parsedContent = CKEditorParser::parseToHtml($sampleContent);

        // Check that HTML entities are decoded
        $this->assertStringContainsString('—', $parsedContent);
        $this->assertStringNotContainsString('&mdash;', $parsedContent);
        $this->assertStringNotContainsString('&#39;', $parsedContent);

        // Check that markdown bold is converted to HTML
        $this->assertStringContainsString('<strong>pharmacogenomics</strong>', $parsedContent);
        $this->assertStringContainsString('<strong>nutrigenomics</strong>', $parsedContent);
        $this->assertStringNotContainsString('**pharmacogenomics**', $parsedContent);

        // Check that headings in paragraphs are converted properly
        $this->assertStringContainsString('<h3>How Does Your DNA Leap from the Lab to Your Lifestyle?</h3>', $parsedContent);
        $this->assertStringNotContainsString('<p>### How Does Your DNA Leap', $parsedContent);

        // Check that unnecessary br tags are removed
        $this->assertStringNotContainsString('<br />', $parsedContent);
    }

    public function test_extract_plain_text()
    {
        $htmlContent = '<h1>Test Title</h1><p>This is a <strong>test</strong> paragraph.</p>';
        
        $plainText = CKEditorParser::extractPlainText($htmlContent);
        
        $this->assertEquals("Test Title\n\nThis is a test paragraph.", $plainText);
    }

    public function test_generate_excerpt()
    {
        $longContent = '<p>' . str_repeat('This is a long sentence. ', 20) . '</p>';
        
        $excerpt = CKEditorParser::generateExcerpt($longContent, 50);
        
        $this->assertLessThanOrEqual(53, strlen($excerpt)); // 50 + "..."
        $this->assertStringEndsWith('...', $excerpt);
    }

    public function test_clean_dangerous_content()
    {
        $dangerousContent = '<p>Safe content</p><script>alert("dangerous")</script><p onclick="alert()">Click me</p>';
        
        $cleanContent = CKEditorParser::clean($dangerousContent);
        
        $this->assertStringNotContainsString('<script>', $cleanContent);
        $this->assertStringNotContainsString('onclick', $cleanContent);
        $this->assertStringContainsString('<p>Safe content</p>', $cleanContent);
    }

    public function test_helper_functions()
    {
        $content = '<p>Test **bold** content</p>';
        
        // Test parse_ckeditor helper
        $parsed = parse_ckeditor($content);
        $this->assertStringContainsString('<strong>bold</strong>', $parsed);

        // Test clean_ckeditor helper
        $cleaned = clean_ckeditor($content);
        $this->assertStringContainsString('<strong>bold</strong>', $cleaned);
        
        // Test ckeditor_excerpt helper
        $excerpt = ckeditor_excerpt($content, 20);
        $this->assertLessThanOrEqual(23, strlen($excerpt));
        
        // Test ckeditor_plain_text helper
        $plainText = ckeditor_plain_text($content);
        $this->assertEquals('Test bold content', $plainText);
    }
}
