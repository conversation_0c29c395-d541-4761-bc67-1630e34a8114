<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use App\Services\CKEditorParser;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register CKEditor Blade directives
        Blade::directive('ckeditor', function ($expression) {
            return "<?php echo App\Services\CKEditorParser::parseToHtml($expression); ?>";
        });

        Blade::directive('ckeditorClean', function ($expression) {
            return "<?php echo App\Services\CKEditorParser::clean($expression); ?>";
        });

        Blade::directive('ckeditorExcerpt', function ($expression) {
            $parts = explode(',', $expression, 2);
            $content = trim($parts[0]);
            $length = isset($parts[1]) ? trim($parts[1]) : '150';
            return "<?php echo App\Services\CKEditorParser::generateExcerpt($content, $length); ?>";
        });
    }
}
