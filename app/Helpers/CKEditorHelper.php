<?php

if (!function_exists('parse_ckeditor')) {
    /**
     * Parse CKEditor content to clean HTML
     *
     * @param string $content
     * @return string
     */
    function parse_ckeditor(string $content): string
    {
        return \App\Services\CKEditorParser::parseToHtml($content);
    }
}

if (!function_exists('clean_ckeditor')) {
    /**
     * Clean and sanitize CKEditor content
     *
     * @param string $content
     * @return string
     */
    function clean_ckeditor(string $content): string
    {
        return \App\Services\CKEditorParser::clean($content);
    }
}

if (!function_exists('ckeditor_excerpt')) {
    /**
     * Generate excerpt from CKEditor content
     *
     * @param string $content
     * @param int $length
     * @return string
     */
    function ckeditor_excerpt(string $content, int $length = 150): string
    {
        return \App\Services\CKEditorParser::generateExcerpt($content, $length);
    }
}

if (!function_exists('ckeditor_plain_text')) {
    /**
     * Extract plain text from CKEditor content
     *
     * @param string $content
     * @return string
     */
    function ckeditor_plain_text(string $content): string
    {
        return \App\Services\CKEditorParser::extractPlainText($content);
    }
}
