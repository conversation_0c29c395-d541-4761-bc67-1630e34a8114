<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CKEditorParser;

class DemoCKEditorParser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ckeditor:demo';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Demonstrate CKEditor content parsing with sample data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $sampleContent = '<h1>The Hidden Journey of Your DNA: From Lab Research to Transforming Your<br />
Real-World Care</h1>

<p>Imagine unlocking the secrets within your DNA&mdash;insights that could<br />
revolutionize your approach to health, wellness, and disease<br />
prevention. What if a simple test could tell you not just about your<br />
ancestry, but about the unique ways your body processes medication or<br />
responds to specific nutrients? For millions across urban America,<br />
this isn&#39;t science fiction&mdash;it&#39;s the new frontier in personalized<br />
healthcare.</p>

<p>Today, understanding your genetic makeup is no longer reserved for<br />
researchers and academics. Innovations in genetic testing, with a<br />
focus on **pharmacogenomics** (how your genes affect medication<br />
response) and **nutrigenomics** (how your genes interact with<br />
nutrition), are turning individual DNA codes into clear, actionable<br />
health insights.</p>

<p>### How Does Your DNA Leap from the Lab to Your Lifestyle?</p>';

        $this->info('=== CKEditor Content Parser Demo ===');
        $this->newLine();

        $this->line('<fg=yellow>Original CKEditor Content:</>');
        $this->line($sampleContent);
        $this->newLine();

        $parsedContent = CKEditorParser::parseToHtml($sampleContent);
        $this->line('<fg=green>Parsed to Clean HTML:</>');
        $this->line($parsedContent);
        $this->newLine();

        $plainText = CKEditorParser::extractPlainText($sampleContent);
        $this->line('<fg=cyan>Plain Text Extraction:</>');
        $this->line($plainText);
        $this->newLine();

        $excerpt = CKEditorParser::generateExcerpt($sampleContent, 150);
        $this->line('<fg=magenta>Excerpt (150 chars):</>');
        $this->line($excerpt);
        $this->newLine();

        $this->info('✓ HTML entities decoded (e.g., &mdash; → —)');
        $this->info('✓ Markdown bold converted (**text** → <strong>text</strong>)');
        $this->info('✓ Headings in paragraphs fixed (### → <h3>)');
        $this->info('✓ Unnecessary <br /> tags removed');
        $this->info('✓ Proper paragraph structure maintained');
        $this->info('✓ Content sanitized for security');

        return Command::SUCCESS;
    }
}
