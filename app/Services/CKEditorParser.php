<?php

namespace App\Services;

class CKEditorParser
{
    /**
     * Parse CKEditor content to clean HTML
     *
     * @param string $content
     * @return string
     */
    public static function parseToHtml(string $content): string
    {
        if (empty($content)) {
            return '';
        }

        // Step 1: Decode HTML entities (may need multiple passes for double-encoded content)
        $content = html_entity_decode($content, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        $content = html_entity_decode($content, ENT_QUOTES | ENT_HTML5, 'UTF-8'); // Second pass for double-encoded

        // Step 2: Fix improperly nested HTML (like <strong><h1>...</h1></strong>)
        // Extract content from improper nesting and restructure
        $content = preg_replace('/<strong>\s*<(h[1-6])([^>]*)>(.*?)<\/\1>\s*<\/strong>/s', '<$1$2>$3</$1>', $content);
        $content = preg_replace('/<strong>\s*<(p)([^>]*)>(.*?)<\/\1>\s*<\/strong>/s', '<$1$2><strong>$3</strong></$1>', $content);

        // Step 3: Convert markdown-style bold text to HTML (but not if already in HTML tags)
        $content = preg_replace('/\*\*(.*?)\*\*/s', '<strong>$1</strong>', $content);

        // Step 4: Fix headings that might be in paragraphs
        $content = preg_replace_callback('/<p([^>]*)>\s*(#{1,6})\s*(.*?)\s*<\/p>/i', function($matches) {
            $level = strlen($matches[2]);
            return "<h{$level}>{$matches[3]}</h{$level}>";
        }, $content);

        // Step 5: Convert markdown-style headings wrapped in strong tags
        $content = preg_replace('/<strong>\s*###\s*(.*?)\s*<\/strong>/m', '<h3>$1</h3>', $content);
        $content = preg_replace('/<strong>\s*##\s*(.*?)\s*<\/strong>/m', '<h2>$1</h2>', $content);
        $content = preg_replace('/<strong>\s*#\s*(.*?)\s*<\/strong>/m', '<h1>$1</h1>', $content);

        // Step 6: Convert standalone markdown headings to proper HTML headings
        $content = preg_replace('/^###\s*(.*?)$/m', '<h3>$1</h3>', $content);
        $content = preg_replace('/^##\s*(.*?)$/m', '<h2>$1</h2>', $content);
        $content = preg_replace('/^#\s*(.*?)$/m', '<h1>$1</h1>', $content);

        // Step 7: Handle <br /> tags within paragraphs - convert to spaces
        $content = preg_replace_callback('/<p([^>]*)>(.*?)<\/p>/s', function($matches) {
            $attrs = $matches[1];
            $innerContent = $matches[2];
            // Replace <br /> with spaces within paragraph content
            $innerContent = preg_replace('/<br\s*\/?>\s*/', ' ', $innerContent);
            // Clean up extra whitespace
            $innerContent = preg_replace('/\s+/', ' ', trim($innerContent));
            return "<p{$attrs}>{$innerContent}</p>";
        }, $content);

        // Step 8: Remove any remaining standalone <br /> tags
        $content = preg_replace('/<br\s*\/?>/i', '', $content);

        // Step 9: Fix nested paragraph tags (like <p><p>content</p></p>)
        $content = preg_replace('/<p([^>]*)>\s*<p([^>]*)>/i', '<p$2>', $content);
        $content = preg_replace('/<\/p>\s*<\/p>/i', '</p>', $content);

        // Step 10: Clean up empty paragraphs
        $content = preg_replace('/<p[^>]*>\s*<\/p>/i', '', $content);

        // Step 11: Fix any malformed HTML structure
        $content = preg_replace('/<p([^>]*)><h([1-6])([^>]*)>/i', '</p><h$2$3>', $content);
        $content = preg_replace('/<\/h([1-6])><\/p>/i', '</h$1>', $content);

        // Step 12: Add proper spacing between block elements
        $content = preg_replace('/(<\/(?:h[1-6]|p|div|blockquote)>)(<(?:h[1-6]|p|div|blockquote))/', '$1' . "\n\n" . '$2', $content);

        // Step 13: Clean up orphaned opening/closing tags
        $content = preg_replace('/^<\/p>\s*/i', '', $content); // Remove orphaned closing </p> at start
        $content = preg_replace('/\s*<p[^>]*>$/i', '', $content); // Remove orphaned opening <p> at end

        // Step 14: Clean up extra whitespace but preserve structure
        $content = preg_replace('/[ \t]+/', ' ', $content);
        $content = preg_replace('/\n\s*\n\s*\n+/', "\n\n", $content);

        return trim($content);
    }

    /**
     * Convert line breaks to paragraphs
     *
     * @param string $content
     * @return string
     */
    private static function convertLineBreaksToParagraphs(string $content): string
    {
        // Split content by double line breaks to identify paragraph boundaries
        $paragraphs = preg_split('/\n\s*\n/', $content);
        $result = [];

        foreach ($paragraphs as $paragraph) {
            $paragraph = trim($paragraph);
            
            if (empty($paragraph)) {
                continue;
            }

            // Skip if it's already a block element
            if (preg_match('/^<(?:h[1-6]|div|blockquote|ul|ol|li|table|tr|td|th)/', $paragraph)) {
                $result[] = $paragraph;
                continue;
            }

            // Skip if it's already wrapped in a paragraph
            if (preg_match('/^<p[^>]*>.*<\/p>$/s', $paragraph)) {
                $result[] = $paragraph;
                continue;
            }

            // Convert single line breaks to spaces within the paragraph
            $paragraph = preg_replace('/\n/', ' ', $paragraph);
            
            // Wrap in paragraph tags
            $result[] = "<p>{$paragraph}</p>";
        }

        return implode("\n\n", $result);
    }

    /**
     * Extract plain text from CKEditor content
     *
     * @param string $content
     * @return string
     */
    public static function extractPlainText(string $content): string
    {
        $html = self::parseToHtml($content);
        return strip_tags($html);
    }

    /**
     * Generate excerpt from CKEditor content
     *
     * @param string $content
     * @param int $length
     * @return string
     */
    public static function generateExcerpt(string $content, int $length = 150): string
    {
        $plainText = self::extractPlainText($content);
        return \Illuminate\Support\Str::limit($plainText, $length);
    }

    /**
     * Clean and validate CKEditor content
     *
     * @param string $content
     * @return string
     */
    public static function clean(string $content): string
    {
        // Remove potentially dangerous tags and attributes
        $allowedTags = '<p><br><strong><b><em><i><u><h1><h2><h3><h4><h5><h6><ul><ol><li><a><img><blockquote><table><tr><td><th><tbody><thead><tfoot>';
        
        $content = strip_tags($content, $allowedTags);
        
        // Remove dangerous attributes
        $content = preg_replace('/(<[^>]+)\s+(on\w+|javascript:|vbscript:|data:)[^>]*>/i', '$1>', $content);
        
        return self::parseToHtml($content);
    }
}
