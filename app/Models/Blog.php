<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;
use App\Services\CKEditorParser;

class Blog extends Model
{
    protected $fillable = [
        'user_id',
        'title',
        'slug',
        'excerpt',
        'content',
        'featured_image',
        'is_published',
        'published_at',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'og_title',
        'og_description',
        'og_image',
        'twitter_title',
        'twitter_description',
        'twitter_image',
        'canonical_url',
    ];

    protected $casts = [
        'is_published' => 'boolean',
        'published_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getRouteKeyName()
    {
        return 'slug';
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($blog) {
            if (empty($blog->slug)) {
                $blog->slug = Str::slug($blog->title);
            }
        });

        static::updating(function ($blog) {
            if ($blog->isDirty('title') && empty($blog->slug)) {
                $blog->slug = Str::slug($blog->title);
            }
        });
    }

    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function getExcerptAttribute($value)
    {
        return $value ?: CKEditorParser::generateExcerpt($this->content, 150);
    }

    /**
     * Get the parsed HTML content
     */
    public function getParsedContentAttribute(): string
    {
        return CKEditorParser::parseToHtml($this->content);
    }

    /**
     * Get the clean HTML content
     */
    public function getCleanContentAttribute(): string
    {
        return CKEditorParser::clean($this->content);
    }

    /**
     * Get plain text content
     */
    public function getPlainTextContentAttribute(): string
    {
        return CKEditorParser::extractPlainText($this->content);
    }
}
